#!/bin/bash

echo "🧹 Cleaning iOS build environment..."

# Kill any running simulators
echo "📱 Stopping iOS Simulator..."
killall Simulator 2>/dev/null || true

# Clean Flutter build cache
echo "🗑️  Cleaning Flutter build cache..."
flutter clean

# Remove iOS build artifacts
echo "🗑️  Removing iOS build artifacts..."
rm -rf ios/Pods
rm -rf ios/.symlinks
rm -rf ios/Flutter/Flutter.framework
rm -rf ios/Flutter/Flutter.podspec
rm -rf ios/Podfile.lock
rm -rf ~/Library/Developer/Xcode/DerivedData

# Clean Xcode build folder
echo "🗑️  Cleaning Xcode build folder..."
cd ios && xcodebuild clean && cd ..

# Get dependencies
echo "📦 Getting Flutter dependencies..."
flutter pub get

# Update iOS pods
echo "📦 Installing iOS pods..."
cd ios && pod deintegrate && pod cache clean --all && pod install && cd ..

# Generate code if needed
echo "🔨 Running code generation..."
flutter packages pub run build_runner build --delete-conflicting-outputs || true

echo "✅ Clean complete! Try running the app again with:"
echo "   flutter run"