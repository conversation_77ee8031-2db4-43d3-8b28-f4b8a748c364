import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/theme/color_palette.dart';
import '../../../../core/theme/typography.dart';
import '../../../../shared/widgets/glass_morphism_card.dart';
import '../../domain/models/workout_session.dart';
import '../../domain/providers/workout_session_provider.dart';

class WorkoutCompletionScreen extends ConsumerStatefulWidget {
  final WorkoutSession workout;

  const WorkoutCompletionScreen({
    super.key,
    required this.workout,
  });

  @override
  ConsumerState<WorkoutCompletionScreen> createState() => _WorkoutCompletionScreenState();
}

class _WorkoutCompletionScreenState extends ConsumerState<WorkoutCompletionScreen>
    with TickerProviderStateMixin {
  late AnimationController _confettiController;
  late AnimationController _slideController;
  late Animation<double> _confettiAnimation;
  late Animation<Offset> _slideAnimation;
  
  int _userRating = 0;
  String _feedback = '';
  bool _isSubmitting = false;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _startCelebration();
  }

  void _setupAnimations() {
    _confettiController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _confettiAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(
      CurvedAnimation(
        parent: _confettiController,
        curve: Curves.easeOut,
      ),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _slideController,
        curve: Curves.elasticOut,
      ),
    );
  }

  void _startCelebration() {
    _confettiController.forward();
    Future.delayed(const Duration(milliseconds: 500), () {
      _slideController.forward();
    });
  }

  @override
  void dispose() {
    _confettiController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final sessionState = ref.watch(workoutSessionProvider);

    return Scaffold(
      backgroundColor: AppColorPalette.darkBackground,
      body: SafeArea(
        child: Stack(
          children: [
            // Confetti animation overlay
            Positioned.fill(
              child: AnimatedBuilder(
                animation: _confettiAnimation,
                builder: (context, child) {
                  return CustomPaint(
                    painter: ConfettiPainter(_confettiAnimation.value),
                  );
                },
              ),
            ),
            
            // Main content
            SlideTransition(
              position: _slideAnimation,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  children: [
                    const SizedBox(height: 40),
                    
                    // Success icon and message
                    _buildSuccessHeader(theme),
                    
                    const SizedBox(height: 32),
                    
                    // Workout stats
                    _buildWorkoutStats(sessionState),
                    
                    const SizedBox(height: 24),
                    
                    // Personal records (if any)
                    _buildPersonalRecords(theme),
                    
                    const SizedBox(height: 24),
                    
                    // Rating section
                    _buildRatingSection(theme),
                    
                    const SizedBox(height: 24),
                    
                    // Feedback section
                    _buildFeedbackSection(theme),
                    
                    const SizedBox(height: 32),
                    
                    // Action buttons
                    _buildActionButtons(theme),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSuccessHeader(ThemeData theme) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: AppColorPalette.accentPrimary.withOpacity(0.2),
            border: Border.all(
              color: AppColorPalette.accentPrimary,
              width: 2,
            ),
          ),
          child: const Icon(
            Icons.check,
            size: 48,
            color: AppColorPalette.accentPrimary,
          ),
        ),
        
        const SizedBox(height: 16),
        
        Text(
          'Workout Complete!',
          style: AppTypography.displayNumbers(
            fontSize: 28,
            color: AppColorPalette.colorCreamWhite,
          ),
          textAlign: TextAlign.center,
        ),
        
        const SizedBox(height: 8),
        
        Text(
          'Great job finishing ${widget.workout.name}',
          style: TextStyle(
            color: AppColorPalette.textCreamSecondary,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildWorkoutStats(WorkoutSessionState sessionState) {
    final durationMinutes = sessionState.elapsedTime.inMinutes;
    final estimatedCalories = (durationMinutes * 8).round();
    
    return GlassMorphismCard(
      child: Column(
        children: [
          const Text(
            'Workout Summary',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          
          const SizedBox(height: 16),
          
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildStatItem(
                icon: Icons.timer,
                value: '${durationMinutes}min',
                label: 'Duration',
              ),
              _buildStatItem(
                icon: Icons.local_fire_department,
                value: '$estimatedCalories',
                label: 'Calories',
              ),
              _buildStatItem(
                icon: Icons.fitness_center,
                value: '${widget.workout.totalSets}',
                label: 'Sets',
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String value,
    required String label,
  }) {
    return Column(
      children: [
        Icon(
          icon,
          color: AppColorPalette.primaryOrange,
          size: 24,
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: AppTypography.displayNumbers(
            fontSize: 20,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            color: Colors.white.withOpacity(0.7),
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildPersonalRecords(ThemeData theme) {
    // TODO: Calculate and display actual PRs from completed sets
    final achievements = <String>[];

    if (achievements.isEmpty) {
      return GlassMorphismCard(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(
                  Icons.emoji_events,
                  color: Colors.amber,
                  size: 20,
                ),
                SizedBox(width: 8),
                Text(
                  'Achievements',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            Text(
              'Keep working to unlock achievements!',
              style: TextStyle(
                color: Colors.white.withOpacity(0.7),
                fontSize: 14,
              ),
            ),
          ],
        ),
      );
    }

    return GlassMorphismCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(
                Icons.emoji_events,
                color: Colors.amber,
                size: 20,
              ),
              SizedBox(width: 8),
              Text(
                'Achievements',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          ...achievements.map((achievement) => Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Row(
              children: [
                const Text(
                  '🏆',
                  style: TextStyle(fontSize: 16),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    achievement,
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.9),
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          )),
        ],
      ),
    );
  }

  Widget _buildRatingSection(ThemeData theme) {
    return GlassMorphismCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'How was your workout?',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          
          const SizedBox(height: 16),
          
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: List.generate(5, (index) {
              final starIndex = index + 1;
              return GestureDetector(
                onTap: () {
                  HapticFeedback.lightImpact();
                  setState(() {
                    _userRating = starIndex;
                  });
                },
                child: Icon(
                  starIndex <= _userRating ? Icons.star : Icons.star_outline,
                  color: starIndex <= _userRating 
                      ? Colors.amber 
                      : Colors.white.withOpacity(0.3),
                  size: 32,
                ),
              );
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildFeedbackSection(ThemeData theme) {
    return GlassMorphismCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Any feedback? (Optional)',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          
          const SizedBox(height: 12),
          
          TextField(
            onChanged: (value) {
              setState(() {
                _feedback = value;
              });
            },
            maxLines: 3,
            style: const TextStyle(color: Colors.white),
            decoration: InputDecoration(
              hintText: 'How did the workout feel? Any suggestions?',
              hintStyle: TextStyle(
                color: Colors.white.withOpacity(0.5),
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: Colors.white.withOpacity(0.3),
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: Colors.white.withOpacity(0.3),
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(
                  color: AppColorPalette.primaryOrange,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(ThemeData theme) {
    return Column(
      children: [
        // Finish workout button
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _isSubmitting ? null : _finishWorkout,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColorPalette.accentPrimary,
              foregroundColor: AppColorPalette.colorCreamWhite,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: _isSubmitting
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      color: AppColorPalette.colorCreamWhite,
                      strokeWidth: 2,
                    ),
                  )
                : const Text(
                    'Finish Workout',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
          ),
        ),
        
        const SizedBox(height: 12),
        
        // Share button
        OutlinedButton(
          onPressed: _shareWorkout,
          style: OutlinedButton.styleFrom(
            foregroundColor: Colors.white,
            side: const BorderSide(color: Colors.white, width: 1),
            padding: const EdgeInsets.symmetric(vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          child: const Text('Share Achievement'),
        ),
      ],
    );
  }

  Future<void> _finishWorkout() async {
    setState(() {
      _isSubmitting = true;
    });

    try {
      // Complete workout in provider
      await ref.read(workoutSessionProvider.notifier).completeWorkout(
        userRating: _userRating > 0 ? _userRating : null,
        userFeedback: _feedback.isNotEmpty ? _feedback : null,
      );

      // Show success feedback with rating
      if (mounted) {
        String ratingMessage = '';
        if (_userRating > 0) {
          ratingMessage = ' ($_userRating/5 stars)';
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Workout completed successfully!$ratingMessage'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 3),
          ),
        );

        // Small delay to show the success message
        await Future.delayed(const Duration(milliseconds: 500));

        // Navigate back to dashboard (check mounted after async operation)
        if (mounted) {
          context.go('/');
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving workout: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  void _shareWorkout() {
    HapticFeedback.lightImpact();
    // TODO: Implement social sharing
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Share feature coming soon!'),
      ),
    );
  }
}

// Custom painter for confetti animation
class ConfettiPainter extends CustomPainter {
  final double animationValue;

  ConfettiPainter(this.animationValue);

  @override
  void paint(Canvas canvas, Size size) {
    if (animationValue == 0) return;

    final paint = Paint();
    final colors = [
      AppColorPalette.primaryOrange,
      Colors.amber,
      Colors.blue,
      Colors.green,
      Colors.red,
    ];

    // Draw confetti particles
    for (int i = 0; i < 30; i++) {
      final x = (i * 37) % size.width;
      final y = animationValue * size.height * (1 + (i % 3) * 0.3);
      
      paint.color = colors[i % colors.length];
      
      canvas.drawCircle(
        Offset(x, y % size.height),
        3,
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(ConfettiPainter oldDelegate) {
    return oldDelegate.animationValue != animationValue;
  }
}