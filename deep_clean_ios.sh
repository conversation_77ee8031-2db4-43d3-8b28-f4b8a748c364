#!/bin/bash

echo "🚨 Performing deep clean for iOS build issues..."

# Stop all Flutter processes
echo "🛑 Stopping Flutter processes..."
killall flutter 2>/dev/null || true
killall dart 2>/dev/null || true

# Clean Flutter cache
echo "🗑️  Cleaning Flutter cache..."
rm -rf ~/.pub-cache/hosted/pub.dev/
rm -rf ~/.pub-cache/git/

# Remove all iOS build artifacts
echo "🗑️  Deep cleaning iOS artifacts..."
cd ios
rm -rf ~/Library/Developer/Xcode/DerivedData/*
rm -rf build/
rm -rf .symlinks/
rm -rf Flutter/Flutter.framework
rm -rf Flutter/Flutter.podspec
rm -rf Flutter/ephemeral/
rm -rf Pods/
rm -rf Podfile.lock
rm -rf .generated/
rm -rf GeneratedPluginRegistrant.*
cd ..

# Clean Flutter
echo "🧹 Flutter clean..."
flutter clean

# Remove build cache
rm -rf build/
rm -rf .dart_tool/
rm -rf .packages
rm -rf .flutter-plugins
rm -rf .flutter-plugins-dependencies

# Get packages
echo "📦 Getting packages..."
flutter pub cache repair
flutter pub get

# Regenerate iOS project files
echo "🔧 Regenerating iOS project..."
cd ios
pod deintegrate
pod cache clean --all
pod setup
pod install --repo-update
cd ..

# Build runner
echo "🏗️  Running code generation..."
flutter packages pub run build_runner build --delete-conflicting-outputs

echo "✅ Deep clean complete!"
echo ""
echo "🔧 Now try running with verbose output to see any issues:"
echo "   flutter run --verbose"
echo ""
echo "💡 If it still crashes, try:"
echo "   1. Restart your iPhone"
echo "   2. Delete the app from your phone"
echo "   3. Run: flutter run --release"