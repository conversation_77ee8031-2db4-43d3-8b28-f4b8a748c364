# iOS 26.0 Beta Crash Fix

## Issue
The app crashes on iOS 26.0 (beta) with a Dart VM JIT compilation error during initialization.

## Root Cause
iOS 26.0 is a beta version that may have compatibility issues with Flutter's Dart VM initialization process.

## Solutions to Try

### 1. Update Minimum iOS Version (Recommended)
The project is currently targeting iOS 12.0, but your device is running iOS 26.0 beta. Update the minimum iOS version:

```bash
# In ios/Podfile, change:
platform :ios, '13.0'
# to:
platform :ios, '15.0'

# Then run:
cd ios && pod update && cd ..
```

### 2. Use TestFlight Build
Since debug builds are failing due to JIT compilation issues, try distributing via TestFlight:

```bash
flutter build ios --release
# Then upload to App Store Connect for TestFlight
```

### 3. Downgrade to Stable iOS
If possible, consider using a device with stable iOS (iOS 18.x) for development until Flutter adds full support for iOS 26.0.

### 4. File a Flutter Issue
Report this to the Flutter team:
- https://github.com/flutter/flutter/issues
- Include: iOS version, Flutter version, and the crash log

### 5. Temporary Workaround
Try running with these flags:
```bash
flutter run --no-sound-null-safety --dart-define=FLUTTER_WEB_USE_SKIA=true
```

## Additional Notes
- The crash occurs in `dart::StubCode::Init()` which suggests low-level Dart VM initialization issues
- This is likely a compatibility issue between Flutter's engine and iOS 26.0 beta
- Release builds compile ahead-of-time (AOT) and might work better than debug JIT builds